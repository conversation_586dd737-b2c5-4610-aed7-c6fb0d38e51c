"""
Main entry point for BrainSpace session management system.
"""

import asyncio
import logging
import sys
import argparse
from pathlib import Path

from config import config
from session_service import run_service, get_service


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration."""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('brainspace_session.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific loggers
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def validate_environment():
    """Validate environment and configuration."""
    print("🔍 Validating configuration...")
    
    # Check if .env file exists
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found. Please create one based on .env.example")
        return False
    
    # Validate configuration
    errors = config.validate_config()
    if errors:
        print("❌ Configuration errors found:")
        for error in errors:
            print(f"   • {error}")
        print("\nPlease check your .env file and fix the issues above.")
        return False
    
    print("✅ Configuration validated successfully")
    return True


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="BrainSpace Session Management System")
    parser.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
        default="INFO",
        help="Set logging level (default: INFO)"
    )
    parser.add_argument(
        "--validate-only", 
        action="store_true",
        help="Only validate configuration and exit"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    print("🚀 BrainSpace Session Management System")
    print("=" * 50)
    
    # Validate environment
    if not validate_environment():
        sys.exit(1)
    
    if args.validate_only:
        print("✅ Configuration validation completed successfully")
        sys.exit(0)
    
    try:
        print("🔧 Starting session management service...")
        logger.info("Starting BrainSpace session management system")
        
        # Run the service
        await run_service()
        
    except KeyboardInterrupt:
        print("\n🛑 Service interrupted by user")
        logger.info("Service interrupted by user")
    except Exception as e:
        print(f"❌ Service failed: {e}")
        logger.error(f"Service failed: {e}")
        sys.exit(1)
    finally:
        print("👋 Session management service stopped")
        logger.info("Session management service stopped")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
