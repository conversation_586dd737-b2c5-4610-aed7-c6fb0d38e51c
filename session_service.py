"""
Session service - main orchestrator for session management system.
Integrates session manager and Telegram bot for continuous operation.
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Optional
from threading import Thread
import time

from config import config
from session_models import SessionNotification
from session_manager import Session<PERSON>anager
from telegram_bot_handler import TelegramBotHandler

logger = logging.getLogger(__name__)


class SessionService:
    """Main service that orchestrates session management and Telegram bot."""
    
    def __init__(self):
        self.session_manager: Optional[SessionManager] = None
        self.telegram_bot: Optional[TelegramBotHandler] = None
        self.running = False
        self._monitoring_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the session service components."""
        try:
            # Initialize session manager with notification callback
            self.session_manager = SessionManager(
                notification_callback=self._handle_session_notification
            )
            
            # Initialize Telegram bot
            self.telegram_bot = TelegramBotHandler(self.session_manager)
            await self.telegram_bot.initialize()
            
            logger.info("Session service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize session service: {e}")
            raise
    
    async def start(self):
        """Start the session service."""
        if not self.session_manager or not self.telegram_bot:
            await self.initialize()
        
        try:
            # Validate configuration
            config_errors = config.validate_config()
            if config_errors:
                error_msg = "Configuration errors: " + ", ".join(config_errors)
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            self.running = True
            
            # Start Telegram bot
            await self.telegram_bot.start_bot()
            
            # Start monitoring task
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            # Send startup notification
            await self._send_startup_notification()
            
            logger.info("Session service started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start session service: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """Stop the session service."""
        logger.info("Stopping session service...")
        
        self.running = False
        
        # Cancel monitoring task
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        # Stop Telegram bot
        if self.telegram_bot:
            await self.telegram_bot.stop_bot()
        
        logger.info("Session service stopped")
    
    async def run_forever(self):
        """Run the service forever with proper error handling."""
        try:
            await self.start()
            
            # Keep running until interrupted
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Service interrupted by user")
        except Exception as e:
            logger.error(f"Service error: {e}")
        finally:
            await self.stop()
    
    async def _monitoring_loop(self):
        """Main monitoring loop for session expiry and health checks."""
        logger.info("Starting monitoring loop")
        
        while self.running:
            try:
                # Check session expiry notifications
                if self.session_manager:
                    self.session_manager.check_expiry_notifications()
                
                # Perform health checks
                await self._perform_health_checks()
                
                # Sleep for 60 seconds before next check
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Continue after error
        
        logger.info("Monitoring loop stopped")
    
    async def _perform_health_checks(self):
        """Perform periodic health checks."""
        try:
            # Check if session manager is responsive
            if self.session_manager:
                session = self.session_manager.get_session()
                if session and session.is_expired():
                    logger.debug("Session expired, will be handled by expiry notifications")
            
            # Check if Telegram bot is responsive
            if self.telegram_bot and self.telegram_bot.application:
                # Bot health is implicitly checked by its ability to receive updates
                pass
            
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
    
    def _handle_session_notification(self, notification: SessionNotification):
        """Handle session notifications by forwarding to Telegram bot."""
        if self.telegram_bot:
            # Run async method in event loop
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(self.telegram_bot.send_notification(notification))
                else:
                    loop.run_until_complete(self.telegram_bot.send_notification(notification))
            except Exception as e:
                logger.error(f"Failed to send notification via Telegram: {e}")
    
    async def _send_startup_notification(self):
        """Send startup notification to user."""
        if self.telegram_bot:
            startup_message = (
                "🚀 <b>BrainSpace Session Manager Started</b>\n\n"
                f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🔧 Session expiry: {config.SESSION_EXPIRY_HOURS} hours\n"
                f"🔔 Notifications: {', '.join(map(str, config.NOTIFY_BEFORE_EXPIRY_MINUTES))} minutes before expiry\n\n"
                "Use /status to check current session or /help for available commands."
            )
            
            notification = SessionNotification(
                message=startup_message,
                notification_type="startup"
            )
            
            await self.telegram_bot.send_notification(notification)
    
    def get_session_manager(self) -> Optional[SessionManager]:
        """Get the session manager instance."""
        return self.session_manager
    
    def get_telegram_bot(self) -> Optional[TelegramBotHandler]:
        """Get the Telegram bot handler instance."""
        return self.telegram_bot
    
    def notify_tg(self, message: str):
        """
        Send custom notification via Telegram bot.
        
        Args:
            message: Message to send
        """
        if self.telegram_bot:
            self.telegram_bot.notify_tg(message)
        else:
            logger.warning("Telegram bot not available for notification")


# Global service instance
_service_instance: Optional[SessionService] = None


def get_service() -> SessionService:
    """Get or create the global service instance."""
    global _service_instance
    if _service_instance is None:
        _service_instance = SessionService()
    return _service_instance


async def run_service():
    """Run the session service as the main application."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('brainspace_session.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Create and run service
    service = get_service()
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(service.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the service
    await service.run_forever()


if __name__ == "__main__":
    # Run the service directly
    asyncio.run(run_service())
