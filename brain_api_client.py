"""
Brain API client for authentication and session management.
Handles standard login and biometric/persona authentication flow.
"""

import requests
import logging
from typing import Op<PERSON>, <PERSON>ple
from urllib.parse import urljoin
from datetime import datetime, timedelta

from config import config
from session_models import AuthenticationResponse, PersonaAuthInfo, APIError, SessionStatus

logger = logging.getLogger(__name__)


class BrainAPIClient:
    """Client for Brain API authentication and session management."""

    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = config.REQUEST_TIMEOUT
        self.base_url = config.BRAIN_API_BASE_URL

    def authenticate(self, username: str, password: str) -> Tuple[AuthenticationResponse, Optional[PersonaAuthInfo]]:
        """
        Authenticate with Brain API.

        Args:
            username: User email
            password: User password

        Returns:
            Tuple of (AuthenticationResponse, PersonaAuthInfo if biometric required)
        """
        try:
            # Set basic auth
            self.session.auth = (username, password)

            # Attempt authentication
            auth_url = f"{self.base_url}/authentication"
            response = self.session.post(auth_url)

            auth_response = AuthenticationResponse(
                status_code=response.status_code,
                headers=dict(response.headers)
            )

            if response.status_code == 201:
                # Successful authentication
                data = response.json()
                auth_response.user = data.get("user", {})
                auth_response.token = data.get("token", {})
                auth_response.permissions = data.get("permissions", [])

                logger.info("Authentication successful")
                return auth_response, None

            elif response.status_code == 401:
                # Check if biometric authentication is required
                www_auth = response.headers.get("WWW-Authenticate", "")
                location = response.headers.get("Location", "")

                if www_auth == "persona" and location:
                    # Biometric authentication required
                    persona_url = urljoin(response.url, location)
                    inquiry_id = self._extract_inquiry_id(location)

                    persona_info = PersonaAuthInfo(
                        inquiry_id=inquiry_id,
                        persona_url=persona_url
                    )

                    logger.info(
                        f"Biometric authentication required: {persona_url}")
                    return auth_response, persona_info
                else:
                    # Invalid credentials
                    logger.error("Invalid email and password")
                    return auth_response, None
            else:
                # Other error
                logger.error(
                    f"Authentication failed with status {response.status_code}")
                return auth_response, None

        except requests.RequestException as e:
            logger.error(f"Authentication request failed: {e}")
            auth_response = AuthenticationResponse(
                status_code=0,
                headers={}
            )
            return auth_response, None

    def complete_persona_auth(self, persona_info: PersonaAuthInfo) -> AuthenticationResponse:
        """
        Complete biometric/persona authentication.

        Args:
            persona_info: Persona authentication information

        Returns:
            AuthenticationResponse with final authentication result
        """
        try:
            logger.info(
                f"Completing persona authentication at: {persona_info.persona_url}")
            response = self.session.post(persona_info.persona_url)

            auth_response = AuthenticationResponse(
                status_code=response.status_code,
                headers=dict(response.headers)
            )

            if response.status_code == 201:
                # Successful authentication after biometric
                try:
                    data = response.json()
                    auth_response.user = data.get("user", {})
                    auth_response.token = data.get("token", {})
                    auth_response.permissions = data.get("permissions", [])

                    logger.info(
                        "Biometric authentication completed successfully")
                    logger.debug(f"Auth response data: {data}")
                except Exception as json_error:
                    logger.error(
                        f"Failed to parse JSON response: {json_error}")
                    logger.debug(f"Response content: {response.text}")
            else:
                logger.error(
                    f"Biometric authentication failed with status {response.status_code}")
                logger.debug(f"Response content: {response.text}")

            return auth_response

        except requests.RequestException as e:
            logger.error(f"Biometric authentication request failed: {e}")
            return AuthenticationResponse(status_code=0, headers={})

    def refresh_session(self) -> AuthenticationResponse:
        """
        Attempt to refresh the current session.

        Returns:
            AuthenticationResponse with refresh result
        """
        try:
            auth_url = f"{self.base_url}/authentication"
            response = self.session.get(auth_url)

            auth_response = AuthenticationResponse(
                status_code=response.status_code,
                headers=dict(response.headers)
            )

            if response.status_code == 200:
                # Session is still valid
                data = response.json()
                auth_response.user = data.get("user", {})
                auth_response.token = data.get("token", {})
                auth_response.permissions = data.get("permissions", [])

                logger.info("Session refresh successful")
            else:
                logger.warning(
                    f"Session refresh failed with status {response.status_code}")

            return auth_response

        except requests.RequestException as e:
            logger.error(f"Session refresh request failed: {e}")
            return AuthenticationResponse(status_code=0, headers={})

    def test_session_validity(self) -> bool:
        """
        Test if current session is valid by making a simple API call.

        Returns:
            True if session is valid, False otherwise
        """
        try:
            # Test with a simple GET request to authentication endpoint
            auth_url = f"{self.base_url}/authentication"
            response = self.session.get(auth_url)
            return response.status_code == 200
        except requests.RequestException:
            return False

    def logout(self) -> bool:
        """
        Logout and clear session.

        Returns:
            True if logout successful, False otherwise
        """
        try:
            auth_url = f"{self.base_url}/authentication"
            response = self.session.delete(auth_url)

            # Clear session auth
            self.session.auth = None

            logger.info("Logout successful")
            return response.status_code in [200, 204]

        except requests.RequestException as e:
            logger.error(f"Logout request failed: {e}")
            return False

    def _extract_inquiry_id(self, location: str) -> str:
        """Extract inquiry ID from persona location header."""
        try:
            # Extract inquiry ID from URL like "/authentication/persona?inquiry=inq_XXXXXX"
            if "inquiry=" in location:
                return location.split("inquiry=")[1].split("&")[0]
            return ""
        except Exception:
            return ""

    def get_session(self) -> requests.Session:
        """Get the underlying requests session for API calls."""
        return self.session
