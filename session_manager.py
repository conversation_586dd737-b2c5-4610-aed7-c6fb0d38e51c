"""
Session manager for handling session lifecycle, token storage, and expiry management.
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import Optional, Callable
from threading import Lock
import time

from config import config
from session_models import SessionData, SessionStatus, PersonaAuthInfo, SessionNotification
from brain_api_client import BrainAPIClient

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages session lifecycle, storage, and expiry handling."""

    def __init__(self, notification_callback: Optional[Callable[[SessionNotification], None]] = None):
        self.session_file = config.SESSION_FILE_PATH
        self.expiry_hours = config.SESSION_EXPIRY_HOURS
        self.notify_minutes = config.NOTIFY_BEFORE_EXPIRY_MINUTES
        self.notification_callback = notification_callback
        self.api_client = BrainAPIClient()
        self._lock = Lock()
        self._session_data: Optional[SessionData] = None
        self._last_notification_check = datetime.now()

        # Load existing session on initialization
        self.load_session()

    def get_session(self) -> Optional[SessionData]:
        """
        Get current session data.

        Returns:
            SessionData if available, None otherwise
        """
        with self._lock:
            if self._session_data and self._session_data.is_valid():
                return self._session_data
            return None

    def is_expired(self) -> bool:
        """Check if current session is expired."""
        with self._lock:
            if not self._session_data:
                return True
            return self._session_data.is_expired()

    def get_valid_token(self) -> Optional[str]:
        """
        Get valid session token, attempting refresh if needed.

        Returns:
            Valid token string or None if no valid session
        """
        session = self.get_session()
        if session:
            return session.token

        # Try to refresh session
        if self.refresh_session():
            session = self.get_session()
            return session.token if session else None

        return None

    def login(self, username: Optional[str] = None, password: Optional[str] = None) -> tuple[bool, Optional[PersonaAuthInfo]]:
        """
        Perform login with Brain API.

        Args:
            username: User email (uses config if not provided)
            password: User password (uses config if not provided)

        Returns:
            Tuple of (success, persona_info_if_biometric_required)
        """
        username = username or config.BRAIN_USER
        password = password or config.BRAIN_PASSWORD

        if not username or not password:
            logger.error("Username and password are required for login")
            return False, None

        try:
            auth_response, persona_info = self.api_client.authenticate(
                username, password)

            if auth_response.status_code == 201:
                # Successful authentication
                self._create_session_from_response(auth_response)
                self._notify_login_success()
                return True, None

            elif auth_response.status_code == 401 and persona_info:
                # Biometric authentication required
                self._update_session_for_persona(persona_info)
                return False, persona_info
            else:
                # Authentication failed
                self._handle_login_failure(auth_response.status_code)
                return False, None

        except Exception as e:
            logger.error(f"Login failed with exception: {e}")
            return False, None

    def complete_persona_login(self, persona_info: PersonaAuthInfo) -> bool:
        """
        Complete biometric/persona authentication.

        Args:
            persona_info: Persona authentication information

        Returns:
            True if authentication successful, False otherwise
        """
        try:
            auth_response = self.api_client.complete_persona_auth(persona_info)

            if auth_response.status_code == 201:
                # Successful biometric authentication
                self._create_session_from_response(auth_response)
                self._notify_login_success()
                return True
            else:
                logger.error(
                    f"Biometric authentication failed with status {auth_response.status_code}")
                return False

        except Exception as e:
            logger.error(
                f"Biometric authentication failed with exception: {e}")
            return False

    def refresh_session(self) -> bool:
        """
        Attempt to refresh current session.

        Returns:
            True if refresh successful, False otherwise
        """
        try:
            auth_response = self.api_client.refresh_session()

            if auth_response.status_code == 200:
                # Update existing session with refreshed data
                with self._lock:
                    if self._session_data:
                        self._session_data.update_expiry(self.expiry_hours)
                        self._session_data.status = SessionStatus.ACTIVE
                        self.save_session()
                        logger.info("Session refreshed successfully")
                        return True

            logger.warning("Session refresh failed")
            return False

        except Exception as e:
            logger.error(f"Session refresh failed with exception: {e}")
            return False

    def logout(self) -> bool:
        """
        Logout and clear session.

        Returns:
            True if logout successful, False otherwise
        """
        try:
            success = self.api_client.logout()

            # Clear local session regardless of API response
            with self._lock:
                self._session_data = None

            # Remove session file
            if os.path.exists(self.session_file):
                os.remove(self.session_file)

            logger.info("Logout completed")
            return success

        except Exception as e:
            logger.error(f"Logout failed with exception: {e}")
            return False

    def check_expiry_notifications(self):
        """Check if user should be notified about session expiry."""
        now = datetime.now()

        # Only check once per minute to avoid spam
        if (now - self._last_notification_check).total_seconds() < 60:
            return

        self._last_notification_check = now

        with self._lock:
            if not self._session_data or not self._session_data.is_valid():
                return

            minutes_left = self._session_data.minutes_until_expiry()

            if self._session_data.should_notify_expiry(self.notify_minutes):
                self._notify_expiry_warning(minutes_left)
            elif self._session_data.is_expired():
                self._notify_session_expired()

    def load_session(self):
        """Load session data from disk."""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'r') as f:
                    data = json.load(f)
                    self._session_data = SessionData(**data)

                    # Check if loaded session is still valid
                    if self._session_data.is_expired():
                        self._session_data.set_expired()
                        logger.info("Loaded session is expired")
                    else:
                        logger.info("Session loaded from disk")
            else:
                logger.info("No existing session file found")

        except Exception as e:
            logger.error(f"Failed to load session from disk: {e}")
            self._session_data = None

    def save_session(self):
        """Save session data to disk."""
        try:
            with self._lock:
                if self._session_data:
                    with open(self.session_file, 'w') as f:
                        json.dump(self._session_data.model_dump(),
                                  f, default=str, indent=2)
                    logger.debug("Session saved to disk")

        except Exception as e:
            logger.error(f"Failed to save session to disk: {e}")

    def _create_session_from_response(self, auth_response):
        """Create session data from authentication response."""
        with self._lock:
            user_id = auth_response.user.get("id", "")
            token_data = auth_response.token

            # Extract token (assuming it's stored in session cookies or headers)
            token = self._extract_token_from_response(auth_response)

            self._session_data = SessionData()
            self._session_data.set_active(token, user_id, self.expiry_hours)
            self._session_data.permissions = auth_response.permissions

            self.save_session()
            logger.info("Session created successfully")

    def _extract_token_from_response(self, auth_response) -> str:
        """Extract token from authentication response."""
        # Try to extract JWT token from the session cookies
        session_cookies = self.api_client.get_session().cookies

        # Look for common JWT cookie names
        jwt_cookie_names = ['jwt', 'token', 'auth_token',
                            'session_token', 'access_token']
        for cookie_name in jwt_cookie_names:
            if cookie_name in session_cookies:
                token = session_cookies[cookie_name]
                logger.debug(f"Found JWT token in cookie '{cookie_name}'")
                return token

        # Try to extract from Authorization header if set
        auth_header = self.api_client.get_session().headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]  # Remove 'Bearer ' prefix
            logger.debug("Found JWT token in Authorization header")
            return token

        # Try to extract from response token field
        if hasattr(auth_response, 'token') and auth_response.token:
            if isinstance(auth_response.token, dict):
                # Look for token in various fields
                for field in ['access_token', 'jwt', 'token', 'value']:
                    if field in auth_response.token:
                        token = auth_response.token[field]
                        logger.debug(
                            f"Found JWT token in response token.{field}")
                        return token
            elif isinstance(auth_response.token, str):
                logger.debug("Found JWT token in response token field")
                return auth_response.token

        # Fallback: generate a session identifier (not a real JWT)
        fallback_token = f"session_{datetime.now().timestamp()}"
        logger.warning(
            "Could not extract JWT token, using fallback session identifier")
        return fallback_token

    def _update_session_for_persona(self, persona_info: PersonaAuthInfo):
        """Update session for pending persona authentication."""
        with self._lock:
            if not self._session_data:
                self._session_data = SessionData()

            self._session_data.status = SessionStatus.PENDING_AUTH
            self._session_data.persona_auth = persona_info
            self.save_session()

    def _notify_login_success(self):
        """Send login success notification."""
        if self.notification_callback:
            notification = SessionNotification(
                message="Login successful! Session is now active.",
                notification_type="login_success"
            )
            self.notification_callback(notification)

    def _notify_expiry_warning(self, minutes_left: int):
        """Send expiry warning notification."""
        if self.notification_callback:
            notification = SessionNotification(
                message=f"⚠️ Session will expire in {minutes_left} minutes. Use /login to refresh.",
                notification_type="expiry_warning",
                minutes_until_expiry=minutes_left
            )
            self.notification_callback(notification)

    def _notify_session_expired(self):
        """Send session expired notification."""
        if self.notification_callback:
            notification = SessionNotification(
                message="🔴 Session has expired. Please use /login to authenticate again.",
                notification_type="expired"
            )
            self.notification_callback(notification)

    def _handle_login_failure(self, status_code: int):
        """Handle login failure."""
        with self._lock:
            if self._session_data:
                self._session_data.set_invalid()
                self.save_session()

        if self.notification_callback:
            notification = SessionNotification(
                message=f"❌ Login failed (status: {status_code}). Please check credentials.",
                notification_type="login_failed"
            )
            self.notification_callback(notification)
