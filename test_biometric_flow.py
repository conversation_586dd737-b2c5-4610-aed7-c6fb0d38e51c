#!/usr/bin/env python3
"""
Test script for biometric authentication flow.
This script helps test the biometric authentication without running the full service.
"""

import asyncio
import logging
from config import config
from session_manager import SessionManager
from session_models import SessionNotification

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def notification_callback(notification: SessionNotification):
    """Handle session notifications."""
    print(f"\n📢 NOTIFICATION ({notification.notification_type}):")
    print(f"   {notification.message}")
    if notification.minutes_until_expiry:
        print(f"   Minutes until expiry: {notification.minutes_until_expiry}")
    print()


async def test_biometric_flow():
    """Test the biometric authentication flow."""
    print("🧪 Testing Biometric Authentication Flow")
    print("=" * 50)
    
    # Validate configuration
    config_errors = config.validate_config()
    if config_errors:
        print("❌ Configuration errors:")
        for error in config_errors:
            print(f"   • {error}")
        return False
    
    print("✅ Configuration validated")
    
    # Create session manager
    session_manager = SessionManager(notification_callback=notification_callback)
    
    print("\n🔐 Attempting login...")
    success, persona_info = session_manager.login()
    
    if success:
        print("✅ Login successful without biometric authentication!")
        session = session_manager.get_session()
        if session:
            print(f"   User ID: {session.user_id}")
            print(f"   Token: {session.token[:20]}..." if session.token else "No token")
            print(f"   Expires at: {session.expires_at}")
        return True
        
    elif persona_info:
        print("🔐 Biometric authentication required!")
        print(f"   Persona URL: {persona_info.persona_url}")
        print(f"   Inquiry ID: {persona_info.inquiry_id}")
        
        print("\n📋 Next steps:")
        print("1. Open the persona URL in your browser")
        print("2. Complete the biometric authentication")
        print("3. Click 'Complete' on the webpage")
        print("4. Press Enter here to continue...")
        
        # Wait for user input
        input("\nPress Enter after completing biometric authentication...")
        
        print("\n🔄 Completing biometric authentication...")
        success = session_manager.complete_persona_login(persona_info)
        
        if success:
            print("✅ Biometric authentication completed successfully!")
            session = session_manager.get_session()
            if session:
                print(f"   User ID: {session.user_id}")
                print(f"   Token: {session.token[:20]}..." if session.token else "No token")
                print(f"   Expires at: {session.expires_at}")
            return True
        else:
            print("❌ Biometric authentication failed!")
            return False
    else:
        print("❌ Login failed!")
        return False


async def test_session_status():
    """Test session status checking."""
    print("\n📊 Testing Session Status")
    print("-" * 30)
    
    session_manager = SessionManager()
    session = session_manager.get_session()
    
    if session:
        print(f"✅ Session found:")
        print(f"   Status: {session.status}")
        print(f"   Valid: {session.is_valid()}")
        print(f"   Expired: {session.is_expired()}")
        if session.expires_at:
            remaining = session.time_until_expiry()
            if remaining:
                hours = int(remaining.total_seconds() // 3600)
                minutes = int((remaining.total_seconds() % 3600) // 60)
                print(f"   Time remaining: {hours:02d}:{minutes:02d}")
    else:
        print("❌ No session found")


async def main():
    """Main test function."""
    try:
        print("🚀 BrainSpace Biometric Authentication Test")
        print("=" * 60)
        
        # Test biometric flow
        success = await test_biometric_flow()
        
        if success:
            # Test session status
            await test_session_status()
            
            print("\n🎉 All tests completed successfully!")
        else:
            print("\n❌ Authentication test failed")
            
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        print(f"\n❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
