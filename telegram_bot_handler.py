"""
Telegram bot handler for session management system.
Implements bot commands: /login, /status, /help
"""

import logging
import asyncio
from typing import Optional, Callable
from datetime import datetime

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
from telegram.constants import ParseMode

from config import config
from session_models import SessionNotification, PersonaAuthInfo, TelegramCommand
from session_manager import SessionManager

logger = logging.getLogger(__name__)


class TelegramBotHandler:
    """Handles Telegram bot interactions for session management."""

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.bot_token = config.TG_BOT_TOKEN
        self.chat_id = int(config.TG_CHAT_ID)
        self.application: Optional[Application] = None
        self._pending_persona_auth: Optional[PersonaAuthInfo] = None

    async def initialize(self):
        """Initialize the Telegram bot application."""
        if not self.bot_token:
            raise ValueError("Telegram bot token is required")

        # Create application
        self.application = Application.builder().token(self.bot_token).build()

        # Add command handlers
        self.application.add_handler(
            CommandHandler("login", self.handle_login))
        self.application.add_handler(
            CommandHandler("status", self.handle_status))
        self.application.add_handler(CommandHandler("help", self.handle_help))
        self.application.add_handler(
            CallbackQueryHandler(self.handle_callback))

        logger.info("Telegram bot initialized")

    async def start_bot(self):
        """Start the Telegram bot."""
        if not self.application:
            await self.initialize()

        # Start the bot
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()

        logger.info("Telegram bot started")

    async def stop_bot(self):
        """Stop the Telegram bot."""
        if self.application:
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()

        logger.info("Telegram bot stopped")

    async def handle_login(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /login command."""
        try:
            await update.message.reply_text("🔄 Attempting to login...")

            # Attempt login
            success, persona_info = self.session_manager.login()

            if success:
                # Login successful
                await update.message.reply_text(
                    "✅ Login successful! Session is now active.",
                    parse_mode=ParseMode.HTML
                )
            elif persona_info:
                # Biometric authentication required
                self._pending_persona_auth = persona_info

                keyboard = [[
                    InlineKeyboardButton(
                        "🔐 Open Biometric Auth", url=persona_info.persona_url)
                ], [
                    InlineKeyboardButton(
                        "✅ I've Completed Authentication", callback_data="persona_completed")
                ]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    "🔐 <b>Biometric Authentication Required</b>\n\n"
                    "1️⃣ Click 'Open Biometric Auth' to complete authentication in your browser\n"
                    "2️⃣ Complete the biometric verification process\n"
                    "3️⃣ Click 'Complete' button on the webpage\n"
                    "4️⃣ Return here and click 'I've Completed Authentication'\n\n"
                    f"🔗 Authentication URL:\n<code>{persona_info.persona_url}</code>",
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )
            else:
                # Login failed
                await update.message.reply_text(
                    "❌ Login failed. Please check your credentials in the configuration.",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            logger.error(f"Error handling login command: {e}")
            await update.message.reply_text("❌ An error occurred during login.")

    async def handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command."""
        try:
            session = self.session_manager.get_session()

            if session and session.is_valid():
                # Active session
                time_remaining = session.time_until_expiry()
                if time_remaining:
                    hours = int(time_remaining.total_seconds() // 3600)
                    minutes = int(
                        (time_remaining.total_seconds() % 3600) // 60)
                    time_str = f"{hours:02d}:{minutes:02d}"
                else:
                    time_str = "00:00"

                status_message = (
                    "🟢 <b>Session Status: ACTIVE</b>\n\n"
                    f"👤 User ID: <code>{session.user_id}</code>\n"
                    f"⏰ Time remaining: <code>{time_str}</code>\n"
                    f"🔄 Last refresh: <code>{session.last_refresh_at.strftime('%Y-%m-%d %H:%M:%S') if session.last_refresh_at else 'N/A'}</code>\n"
                    f"📅 Expires at: <code>{session.expires_at.strftime('%Y-%m-%d %H:%M:%S') if session.expires_at else 'N/A'}</code>"
                )
            elif session and session.status.value == "pending_auth":
                # Pending authentication
                status_message = (
                    "🟡 <b>Session Status: PENDING AUTHENTICATION</b>\n\n"
                    "Biometric authentication is required. Use /login to continue."
                )
            else:
                # No active session
                status_message = (
                    "🔴 <b>Session Status: EXPIRED/INVALID</b>\n\n"
                    "No active session found. Use /login to authenticate."
                )

            await update.message.reply_text(status_message, parse_mode=ParseMode.HTML)

        except Exception as e:
            logger.error(f"Error handling status command: {e}")
            await update.message.reply_text("❌ An error occurred while checking status.")

    async def handle_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        help_message = (
            "🤖 <b>BrainSpace Session Manager Bot</b>\n\n"
            "<b>Available Commands:</b>\n\n"
            "🔐 <code>/login</code> - Authenticate with Brain API\n"
            "   • Triggers new authentication attempt\n"
            "   • Handles biometric authentication if required\n"
            "   • Refreshes existing session\n\n"
            "📊 <code>/status</code> - Check current session status\n"
            "   • Shows active/expired status\n"
            "   • Displays remaining time until expiry\n"
            "   • Shows session details\n\n"
            "❓ <code>/help</code> - Show this help message\n\n"
            "<b>Automatic Notifications:</b>\n"
            "• 🔔 Expiry warnings (60 & 30 minutes before)\n"
            "• 🔴 Session expired alerts\n"
            "• ✅ Login success confirmations\n\n"
            "<i>This bot helps manage your Brain API session automatically.</i>"
        )

        await update.message.reply_text(help_message, parse_mode=ParseMode.HTML)

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries from inline keyboards."""
        query = update.callback_query
        await query.answer()

        if query.data == "persona_completed":
            if self._pending_persona_auth:
                try:
                    await query.edit_message_text(
                        "🔄 <b>Completing biometric authentication...</b>\n\n"
                        "Please wait while we verify your authentication.",
                        parse_mode=ParseMode.HTML
                    )

                    # Complete persona authentication
                    success = self.session_manager.complete_persona_login(
                        self._pending_persona_auth)

                    if success:
                        await query.edit_message_text(
                            "✅ <b>Biometric authentication completed successfully!</b>\n\n"
                            "🟢 Your session is now active and ready to use.\n"
                            "📊 Use /status to check session details.",
                            parse_mode=ParseMode.HTML
                        )
                        self._pending_persona_auth = None
                        logger.info(
                            "Biometric authentication completed successfully via Telegram")
                    else:
                        await query.edit_message_text(
                            "❌ <b>Biometric authentication failed.</b>\n\n"
                            "This could happen if:\n"
                            "• You haven't completed the biometric process yet\n"
                            "• The authentication session has expired\n"
                            "• There was a network error\n\n"
                            "Please try /login again to get a new authentication link.",
                            parse_mode=ParseMode.HTML
                        )
                        logger.warning(
                            "Biometric authentication failed via Telegram")

                except Exception as e:
                    logger.error(
                        f"Error completing persona authentication: {e}")
                    await query.edit_message_text(
                        "❌ <b>An error occurred during authentication.</b>\n\n"
                        "Please try /login again to start a new authentication process.",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await query.edit_message_text(
                    "❌ <b>No pending biometric authentication found.</b>\n\n"
                    "Please use /login to start a new authentication process.",
                    parse_mode=ParseMode.HTML
                )

    async def send_notification(self, notification: SessionNotification):
        """Send notification message to user."""
        try:
            if not self.application:
                logger.warning("Bot not initialized, cannot send notification")
                return

            # Format message based on notification type
            if notification.notification_type == "expiry_warning":
                message = f"⚠️ <b>Session Expiry Warning</b>\n\n{notification.message}"
            elif notification.notification_type == "expired":
                message = f"🔴 <b>Session Expired</b>\n\n{notification.message}"
            elif notification.notification_type == "login_success":
                message = f"✅ <b>Login Successful</b>\n\n{notification.message}"
            elif notification.notification_type == "login_failed":
                message = f"❌ <b>Login Failed</b>\n\n{notification.message}"
            else:
                message = notification.message

            # Send message
            await self.application.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=ParseMode.HTML
            )

            logger.info(f"Notification sent: {notification.notification_type}")

        except Exception as e:
            logger.error(f"Failed to send notification: {e}")

    def notify_tg(self, message: str):
        """
        Send custom notification message (synchronous wrapper).

        Args:
            message: Message to send
        """
        notification = SessionNotification(
            message=message,
            notification_type="custom"
        )

        # Run async method in event loop
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is already running, schedule the coroutine
                asyncio.create_task(self.send_notification(notification))
            else:
                # If no loop is running, run it
                loop.run_until_complete(self.send_notification(notification))
        except Exception as e:
            logger.error(f"Failed to send custom notification: {e}")

    async def run_forever(self):
        """Run the bot forever with proper error handling."""
        try:
            await self.start_bot()

            # Keep the bot running
            while True:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Bot error: {e}")
        finally:
            await self.stop_bot()
