"""
Examples of how to use the session management system to make Brain API requests.
"""

import json
import time
from typing import Optional, Dict, Any

from session_manager import session_manager
from brain_api_client import BrainAPIClient
from config import config


class BrainAPIService:
    """High-level service for making Brain API requests with session management."""
    
    def __init__(self):
        self.client = BrainAPIClient()
    
    def _get_token(self) -> Optional[str]:
        """Get current valid session token."""
        session = session_manager.get_session()
        if session:
            return session.token
        return None
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict[Any, Any]]:
        """Make an authenticated request and return JSON response."""
        token = self._get_token()
        if not token:
            print("❌ No valid session. Please run /login in Telegram bot first.")
            return None
        
        try:
            response = self.client.make_authenticated_request(method, endpoint, token, **kwargs)
            
            if response.status_code in [200, 201]:
                return response.json() if response.content else {}
            else:
                print(f"❌ Request failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Request error: {e}")
            return None
    
    def submit_simulation(self, alpha_expression: str, settings: Optional[Dict] = None) -> Optional[str]:
        """
        Submit an alpha simulation.
        
        Args:
            alpha_expression: The alpha expression to simulate
            settings: Optional simulation settings
            
        Returns:
            Simulation ID if successful, None otherwise
        """
        default_settings = {
            'instrumentType': 'EQUITY',
            'region': 'USA',
            'universe': 'TOP3000',
            'delay': 1,
            'decay': 15,
            'neutralization': 'SUBINDUSTRY',
            'truncation': 0.08,
            'maxTrade': 'ON',
            'pasteurization': 'ON',
            'testPeriod': 'P1Y6M',
            'unitHandling': 'VERIFY',
            'nanHandling': 'OFF',
            'language': 'FASTEXPR',
            'visualization': False,
        }
        
        if settings:
            default_settings.update(settings)
        
        simulation_data = {
            'type': 'REGULAR',
            'settings': default_settings,
            'regular': alpha_expression
        }
        
        print(f"🚀 Submitting simulation: {alpha_expression}")
        
        token = self._get_token()
        if not token:
            return None
        
        try:
            response = self.client.make_authenticated_request(
                'POST', '/simulations', token, json=simulation_data
            )
            
            if response.status_code == 201:
                simulation_url = response.headers.get('Location', '')
                simulation_id = simulation_url.split('/')[-1] if simulation_url else 'unknown'
                print(f"✅ Simulation submitted: {simulation_id}")
                return simulation_id
            else:
                print(f"❌ Simulation failed: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Simulation error: {e}")
            return None
    
    def get_simulation_status(self, simulation_id: str) -> Optional[Dict]:
        """Get simulation status and progress."""
        print(f"📊 Checking simulation status: {simulation_id}")
        return self._make_request('GET', f'/simulations/{simulation_id}')
    
    def wait_for_simulation(self, simulation_id: str, max_wait_minutes: int = 30) -> Optional[str]:
        """
        Wait for simulation to complete and return alpha ID.
        
        Args:
            simulation_id: The simulation ID to wait for
            max_wait_minutes: Maximum time to wait in minutes
            
        Returns:
            Alpha ID if successful, None otherwise
        """
        print(f"⏳ Waiting for simulation {simulation_id} to complete...")
        
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        
        while time.time() - start_time < max_wait_seconds:
            status = self.get_simulation_status(simulation_id)
            
            if not status:
                return None
            
            # Check if simulation is complete
            if 'alpha' in status:
                alpha_id = status['alpha']
                print(f"✅ Simulation complete! Alpha ID: {alpha_id}")
                return alpha_id
            
            # Check for retry-after header
            retry_after = status.get('retryAfter', 30)
            print(f"⏳ Still running... checking again in {retry_after} seconds")
            time.sleep(retry_after)
        
        print(f"⏰ Simulation timed out after {max_wait_minutes} minutes")
        return None
    
    def get_alpha_details(self, alpha_id: str) -> Optional[Dict]:
        """Get alpha details and performance metrics."""
        print(f"📈 Getting alpha details: {alpha_id}")
        return self._make_request('GET', f'/alphas/{alpha_id}')
    
    def get_alpha_pnl(self, alpha_id: str) -> Optional[Dict]:
        """Get alpha PnL data."""
        print(f"💰 Getting PnL data for alpha: {alpha_id}")
        return self._make_request('GET', f'/alphas/{alpha_id}/recordsets/pnl')
    
    def get_alpha_recordsets(self, alpha_id: str) -> Optional[Dict]:
        """Get available recordsets for an alpha."""
        print(f"📋 Getting recordsets for alpha: {alpha_id}")
        return self._make_request('GET', f'/alphas/{alpha_id}/recordsets')
    
    def get_recordset_data(self, alpha_id: str, recordset_name: str) -> Optional[Dict]:
        """Get specific recordset data for an alpha."""
        print(f"📊 Getting {recordset_name} data for alpha: {alpha_id}")
        return self._make_request('GET', f'/alphas/{alpha_id}/recordsets/{recordset_name}')


# Example usage functions
def example_simple_simulation():
    """Example: Submit a simple simulation and get results."""
    print("🧪 Example: Simple Alpha Simulation")
    print("=" * 40)
    
    service = BrainAPIService()
    
    # Submit simulation
    simulation_id = service.submit_simulation("close")
    if not simulation_id:
        return
    
    # Wait for completion
    alpha_id = service.wait_for_simulation(simulation_id, max_wait_minutes=10)
    if not alpha_id:
        return
    
    # Get results
    alpha_details = service.get_alpha_details(alpha_id)
    if alpha_details:
        print(f"📈 Alpha Performance:")
        print(f"   Sharpe: {alpha_details.get('sharpe', 'N/A')}")
        print(f"   Returns: {alpha_details.get('returns', 'N/A')}")
    
    # Get PnL data
    pnl_data = service.get_alpha_pnl(alpha_id)
    if pnl_data and 'records' in pnl_data:
        print(f"💰 PnL Records: {len(pnl_data['records'])} entries")


def example_custom_settings():
    """Example: Submit simulation with custom settings."""
    print("🧪 Example: Custom Settings Simulation")
    print("=" * 40)
    
    service = BrainAPIService()
    
    custom_settings = {
        'region': 'EUR',
        'universe': 'TOP1000',
        'delay': 2,
        'testPeriod': 'P2Y'
    }
    
    simulation_id = service.submit_simulation("rank(close)", custom_settings)
    if simulation_id:
        print(f"✅ Custom simulation submitted: {simulation_id}")


def example_check_existing_alpha():
    """Example: Check details of an existing alpha."""
    print("🧪 Example: Check Existing Alpha")
    print("=" * 40)
    
    service = BrainAPIService()
    
    # Replace with actual alpha ID
    alpha_id = input("Enter Alpha ID to check: ").strip()
    if not alpha_id:
        print("❌ No alpha ID provided")
        return
    
    # Get alpha details
    details = service.get_alpha_details(alpha_id)
    if details:
        print("📈 Alpha Details:")
        print(json.dumps(details, indent=2))
    
    # Get available recordsets
    recordsets = service.get_alpha_recordsets(alpha_id)
    if recordsets and 'results' in recordsets:
        print("\n📋 Available Recordsets:")
        for rs in recordsets['results']:
            print(f"   - {rs['name']}: {rs['title']}")


if __name__ == "__main__":
    print("🧠 Brain API Examples")
    print("Make sure you have an active session (run /login in Telegram bot first)")
    print()
    
    examples = {
        '1': ('Simple Simulation', example_simple_simulation),
        '2': ('Custom Settings', example_custom_settings),
        '3': ('Check Existing Alpha', example_check_existing_alpha)
    }
    
    print("Available examples:")
    for key, (name, _) in examples.items():
        print(f"  {key}. {name}")
    
    choice = input("\nSelect example (1-3): ").strip()
    
    if choice in examples:
        _, example_func = examples[choice]
        print()
        example_func()
    else:
        print("❌ Invalid choice")
