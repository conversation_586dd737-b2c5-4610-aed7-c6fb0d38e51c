# BrainSpace Session Management System

A comprehensive session management system for the Brain API with Telegram bot integration, featuring biometric authentication support and automatic session lifecycle management.

## Features

- **Automated Authentication**: Handles standard login and biometric/persona authentication flows
- **Session Lifecycle Management**: Automatic token storage, expiry handling, and refresh
- **Telegram Bot Integration**: Interactive bot with `/login`, `/status`, and `/help` commands
- **Expiry Notifications**: Automatic alerts at 60 and 30 minutes before session expiry
- **Persistent Storage**: Session data stored securely on disk with automatic recovery
- **Error Handling**: Robust error handling with retry logic and graceful degradation

## Architecture

The system consists of several key components:

1. **`config.py`** - Configuration management and environment variable handling
2. **`session_models.py`** - Pydantic data models for session data and API responses
3. **`brain_api_client.py`** - Brain API client for authentication and session management
4. **`session_manager.py`** - Core session lifecycle management
5. **`telegram_bot_handler.py`** - Telegram bot implementation with command handlers
6. **`session_service.py`** - Main orchestrator that integrates all components
7. **`main.py`** - Entry point for the application

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment

Copy `.env.example` to `.env` and fill in your credentials:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

```env
# Telegram Bot Configuration
tg_bot_token=YOUR_TELEGRAM_BOT_TOKEN
tg_chat_id=YOUR_TELEGRAM_CHAT_ID

# Brain API Credentials
BRAIN_USER=<EMAIL>
BRAIN_PASSWORD=your_password

# Session Configuration
SESSION_FILE_PATH=session_data.json
SESSION_EXPIRY_HOURS=4
NOTIFY_BEFORE_EXPIRY_MINUTES=60,30

# Database Configuration (for future use)
DB_HOST=localhost
DB_DATABASE=brainspace
DB_USER=postgres
DB_PASSWORD=password
DB_PORT=5432
```

### 3. Create Telegram Bot

1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Create a new bot with `/newbot`
3. Get your bot token and add it to `.env`
4. Get your chat ID by messaging [@userinfobot](https://t.me/userinfobot)

## Usage

### Start the Service

```bash
python main.py
```

### Command Line Options

```bash
python main.py --help
```

Options:
- `--log-level`: Set logging level (DEBUG, INFO, WARNING, ERROR)
- `--validate-only`: Only validate configuration and exit

### Telegram Bot Commands

Once the service is running, you can interact with it via Telegram:

- **`/login`** - Authenticate with Brain API
  - Handles standard username/password authentication
  - Supports biometric authentication flow
  - Provides clickable links for biometric completion

- **`/status`** - Check current session status
  - Shows active/expired status
  - Displays remaining time until expiry
  - Shows session details and last refresh time

- **`/help`** - Show available commands and help information

### Authentication Flow

1. **Standard Login**: The system attempts to authenticate with your credentials
2. **Biometric Required**: If biometric authentication is required:
   - Bot sends you a secure biometric link
   - Complete biometric authentication in your browser
   - Click "I've Completed It" button in Telegram
   - System completes the authentication process

### Automatic Notifications

The system automatically sends notifications for:
- **Expiry Warnings**: 60 and 30 minutes before session expires
- **Session Expired**: When session has expired
- **Login Success**: When authentication is successful
- **Login Failures**: When authentication fails

## Session Storage

Sessions are stored in `session_data.json` (configurable) with:
- Session token and user information
- Expiry timestamps
- Authentication status
- Biometric authentication details (if applicable)

## Logging

Logs are written to:
- `brainspace_session.log` (file)
- Console output

Log levels can be configured via command line or environment variables.

## API Integration

The system integrates with the Brain API following the documented authentication flow:

1. **POST** `/authentication` - Initial authentication
2. **GET** `/authentication` - Session validation/refresh
3. **DELETE** `/authentication` - Logout
4. Biometric flow via persona URLs when required

## Error Handling

The system includes comprehensive error handling:
- Network failures with retry logic
- Session expiry with automatic refresh attempts
- Configuration validation
- Graceful degradation when services are unavailable

## Development

### Running in Development Mode

```bash
python main.py --log-level DEBUG
```

### Configuration Validation

```bash
python main.py --validate-only
```

## Troubleshooting

### Common Issues

1. **Bot not responding**: Check bot token and chat ID in `.env`
2. **Authentication failures**: Verify Brain API credentials
3. **Session not persisting**: Check file permissions for session storage
4. **Notifications not working**: Ensure Telegram bot has permission to send messages

### Logs

Check `brainspace_session.log` for detailed error information and debugging.

## Security Considerations

- Session tokens are stored locally in JSON format
- Credentials are loaded from environment variables
- Biometric authentication uses secure persona URLs
- All API communications use HTTPS

## Future Enhancements

- Database integration for session history
- Multi-user support
- Web dashboard for session monitoring
- Advanced retry strategies
- Session analytics and reporting
