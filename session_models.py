"""
Pydantic models for session management system.
Defines data structures for session data, authentication responses, and token management.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum


class SessionStatus(str, Enum):
    """Session status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    INVALID = "invalid"
    PENDING_AUTH = "pending_auth"


class AuthenticationResponse(BaseModel):
    """Model for Brain API authentication response."""
    user: Dict[str, Any] = Field(default_factory=dict)
    token: Dict[str, Any] = Field(default_factory=dict)
    permissions: List[str] = Field(default_factory=list)
    status_code: int
    headers: Dict[str, str] = Field(default_factory=dict)


class PersonaAuthInfo(BaseModel):
    """Model for persona/biometric authentication information."""
    inquiry_id: str
    persona_url: str
    created_at: datetime = Field(default_factory=datetime.now)
    completed: bool = False


class SessionData(BaseModel):
    """Model for session data storage."""
    token: Optional[str] = None
    user_id: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    last_refresh_at: Optional[datetime] = None
    status: SessionStatus = SessionStatus.PENDING_AUTH
    persona_auth: Optional[PersonaAuthInfo] = None
    permissions: List[str] = Field(default_factory=list)
    
    def is_expired(self) -> bool:
        """Check if session is expired."""
        if not self.expires_at:
            return True
        return datetime.now() >= self.expires_at
    
    def is_valid(self) -> bool:
        """Check if session is valid and active."""
        return (
            self.status == SessionStatus.ACTIVE and
            self.token is not None and
            not self.is_expired()
        )
    
    def time_until_expiry(self) -> Optional[timedelta]:
        """Get time remaining until session expires."""
        if not self.expires_at:
            return None
        remaining = self.expires_at - datetime.now()
        return remaining if remaining.total_seconds() > 0 else timedelta(0)
    
    def minutes_until_expiry(self) -> int:
        """Get minutes remaining until session expires."""
        time_remaining = self.time_until_expiry()
        if not time_remaining:
            return 0
        return int(time_remaining.total_seconds() / 60)
    
    def should_notify_expiry(self, notify_minutes: List[int]) -> bool:
        """Check if user should be notified about upcoming expiry."""
        minutes_left = self.minutes_until_expiry()
        return minutes_left in notify_minutes
    
    def update_expiry(self, expiry_hours: int):
        """Update session expiry time."""
        self.expires_at = datetime.now() + timedelta(hours=expiry_hours)
        self.last_refresh_at = datetime.now()
    
    def set_active(self, token: str, user_id: str, expiry_hours: int):
        """Set session as active with token and user info."""
        self.token = token
        self.user_id = user_id
        self.status = SessionStatus.ACTIVE
        self.update_expiry(expiry_hours)
    
    def set_expired(self):
        """Mark session as expired."""
        self.status = SessionStatus.EXPIRED
    
    def set_invalid(self):
        """Mark session as invalid."""
        self.status = SessionStatus.INVALID
        self.token = None


class SessionNotification(BaseModel):
    """Model for session-related notifications."""
    message: str
    notification_type: str  # 'expiry_warning', 'expired', 'login_required', 'login_success'
    timestamp: datetime = Field(default_factory=datetime.now)
    minutes_until_expiry: Optional[int] = None


class TelegramCommand(BaseModel):
    """Model for Telegram bot commands."""
    command: str
    user_id: int
    chat_id: int
    message_id: int
    timestamp: datetime = Field(default_factory=datetime.now)


class APIError(BaseModel):
    """Model for API error responses."""
    status_code: int
    message: str
    headers: Dict[str, str] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)
    retry_count: int = 0
