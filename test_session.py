"""
Simple test script for BrainSpace session management.
"""

import asyncio
import logging
from config import config
from session_manager import session_manager
from telegram_bot_handler import bot_handler

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_configuration():
    """Test configuration validation."""
    print("🔧 Testing Configuration...")
    
    errors = config.validate_config()
    if errors:
        print("❌ Configuration errors found:")
        for error in errors:
            print(f"   - {error}")
        return False
    else:
        print("✅ Configuration is valid")
        return True


def test_session_manager():
    """Test session manager basic functionality."""
    print("\n📋 Testing Session Manager...")
    
    try:
        # Test session status
        status = session_manager.get_session_status()
        print(f"✅ Session status: {status}")
        
        # Test authentication check
        is_auth = session_manager.is_authenticated()
        print(f"✅ Is authenticated: {is_auth}")
        
        return True
        
    except Exception as e:
        print(f"❌ Session manager error: {e}")
        return False


async def test_telegram_bot():
    """Test Telegram bot setup (without starting)."""
    print("\n🤖 Testing Telegram Bot Setup...")
    
    try:
        # Test bot token validation
        if not config.TG_BOT_TOKEN or config.TG_BOT_TOKEN == "YOUR_TG_BOT_TOKEN":
            print("❌ Telegram bot token not configured")
            return False
        
        if not config.TG_CHAT_ID or config.TG_CHAT_ID == "YOUR_TG_CHAT_ID":
            print("❌ Telegram chat ID not configured")
            return False
        
        print("✅ Telegram configuration looks good")
        return True
        
    except Exception as e:
        print(f"❌ Telegram bot error: {e}")
        return False


async def test_authentication():
    """Test authentication flow (if credentials are configured)."""
    print("\n🔐 Testing Authentication...")
    
    if not config.BRAIN_USER or config.BRAIN_USER == "your_email":
        print("⚠️  Brain API credentials not configured - skipping auth test")
        return True
    
    try:
        print("Attempting authentication...")
        auth_response = session_manager.authenticate()
        
        if auth_response.success:
            print("✅ Authentication successful!")
            print(f"   Token expires: {auth_response.expiry}")
        elif auth_response.requires_biometric:
            print("🔒 Biometric authentication required")
            print(f"   Biometric URL: {auth_response.biometric_url}")
        else:
            print(f"❌ Authentication failed: {auth_response.error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 BrainSpace Session Management Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration()),
        ("Session Manager", test_session_manager()),
        ("Telegram Bot", test_telegram_bot()),
        ("Authentication", test_authentication())
    ]
    
    results = []
    for test_name, test_coro in tests:
        if asyncio.iscoroutine(test_coro):
            result = await test_coro
        else:
            result = test_coro
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTests passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The system is ready to use.")
        print("\nTo start the application:")
        print("   python main.py")
    else:
        print("\n⚠️  Some tests failed. Please check your configuration.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env")
        print("2. Fill in your actual credentials")
        print("3. Run tests again: python test_session.py")


if __name__ == "__main__":
    asyncio.run(main())
